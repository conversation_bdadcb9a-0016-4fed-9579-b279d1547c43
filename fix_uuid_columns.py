#!/usr/bin/env python3
"""
Quick script to fix UUID column definitions
"""

import os
import re
from pathlib import Path

def fix_uuid_columns(file_path: Path):
    """Fix UUID column definitions in a single file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Replace UUID(as_uuid=True) with UUID()
        content = re.sub(r'UUID\(as_uuid=True\)', 'UUID()', content)
        
        # Only write if content changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Fixed UUID columns in {file_path}")
            return True
        
        return False
        
    except Exception as e:
        print(f"❌ Error fixing {file_path}: {e}")
        return False

def main():
    """Main function to fix all UUID columns."""
    src_dir = Path("src")
    
    if not src_dir.exists():
        print("❌ src directory not found!")
        return
    
    print("🔧 Fixing UUID column definitions...")
    
    fixed_count = 0
    total_count = 0
    
    # Find all Python files in models directories
    for py_file in src_dir.rglob("*.py"):
        if "models" in str(py_file):
            total_count += 1
            if fix_uuid_columns(py_file):
                fixed_count += 1
    
    print(f"\n📊 Summary:")
    print(f"   Total model files processed: {total_count}")
    print(f"   Files with fixes: {fixed_count}")
    print(f"   Files unchanged: {total_count - fixed_count}")
    
    if fixed_count > 0:
        print("\n✅ UUID column fixes completed!")
    else:
        print("\n✅ No UUID column fixes needed!")

if __name__ == "__main__":
    main()
