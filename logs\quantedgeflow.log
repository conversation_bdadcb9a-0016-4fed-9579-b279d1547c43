{"timestamp": "2025-07-16T15:28:46.819986", "level": "INFO", "logger": "src.utils.logger", "message": "Logging system initialized", "module": "logger", "function": "setup_logging", "line": 196}
{"timestamp": "2025-07-16T15:29:09.753289", "level": "ERROR", "logger": "src.main", "message": "Unhandled exception: RateLimitMiddleware.__call__() takes 3 positional arguments but 4 were given", "module": "main", "function": "global_exception_handler", "line": 160, "exception": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\starlette\\middleware\\errors.py\", line 164, in __call__\n    await self.app(scope, receive, _send)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nTypeError: RateLimitMiddleware.__call__() takes 3 positional arguments but 4 were given"}
{"timestamp": "2025-07-16T15:29:10.526033", "level": "ERROR", "logger": "src.main", "message": "Unhandled exception: RateLimitMiddleware.__call__() takes 3 positional arguments but 4 were given", "module": "main", "function": "global_exception_handler", "line": 160, "exception": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\starlette\\middleware\\errors.py\", line 164, in __call__\n    await self.app(scope, receive, _send)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nTypeError: RateLimitMiddleware.__call__() takes 3 positional arguments but 4 were given"}
{"timestamp": "2025-07-16T15:29:16.717692", "level": "ERROR", "logger": "src.main", "message": "Unhandled exception: RateLimitMiddleware.__call__() takes 3 positional arguments but 4 were given", "module": "main", "function": "global_exception_handler", "line": 160, "exception": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\starlette\\middleware\\errors.py\", line 164, in __call__\n    await self.app(scope, receive, _send)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nTypeError: RateLimitMiddleware.__call__() takes 3 positional arguments but 4 were given"}
{"timestamp": "2025-07-16T15:29:17.375026", "level": "ERROR", "logger": "src.main", "message": "Unhandled exception: RateLimitMiddleware.__call__() takes 3 positional arguments but 4 were given", "module": "main", "function": "global_exception_handler", "line": 160, "exception": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\starlette\\middleware\\errors.py\", line 164, in __call__\n    await self.app(scope, receive, _send)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nTypeError: RateLimitMiddleware.__call__() takes 3 positional arguments but 4 were given"}
{"timestamp": "2025-07-16T15:29:24.498226", "level": "ERROR", "logger": "src.main", "message": "Unhandled exception: RateLimitMiddleware.__call__() takes 3 positional arguments but 4 were given", "module": "main", "function": "global_exception_handler", "line": 160, "exception": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\starlette\\middleware\\errors.py\", line 164, in __call__\n    await self.app(scope, receive, _send)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nTypeError: RateLimitMiddleware.__call__() takes 3 positional arguments but 4 were given"}
{"timestamp": "2025-07-16T15:29:59.963569", "level": "INFO", "logger": "src.utils.logger", "message": "Logging system initialized", "module": "logger", "function": "setup_logging", "line": 196}
{"timestamp": "2025-07-16T15:30:29.523711", "level": "INFO", "logger": "src.utils.logger", "message": "Logging system initialized", "module": "logger", "function": "setup_logging", "line": 196}
{"timestamp": "2025-07-16T15:30:48.802749", "level": "INFO", "logger": "src.utils.logger", "message": "Logging system initialized", "module": "logger", "function": "setup_logging", "line": 196}
{"timestamp": "2025-07-16T15:30:48.841418", "level": "INFO", "logger": "src.main", "message": "Starting QuantEdgeFlow application...", "module": "main", "function": "lifespan", "line": 35}
{"timestamp": "2025-07-16T15:30:48.856620", "level": "ERROR", "logger": "src.config.database", "message": "Database initialization failed: Invalid argument(s) 'pool_size','max_overflow' sent to create_engine(), using configuration SQLiteDialect_aiosqlite/NullPool/Engine.  Please check that the keyword arguments are appropriate for this combination of components.", "module": "database", "function": "init_database", "line": 196}
{"timestamp": "2025-07-16T15:31:14.945643", "level": "INFO", "logger": "src.utils.logger", "message": "Logging system initialized", "module": "logger", "function": "setup_logging", "line": 196}
{"timestamp": "2025-07-16T15:31:14.992003", "level": "INFO", "logger": "src.main", "message": "Starting QuantEdgeFlow application...", "module": "main", "function": "lifespan", "line": 35}
{"timestamp": "2025-07-16T15:31:15.009513", "level": "ERROR", "logger": "src.config.database", "message": "Database initialization failed: Invalid argument(s) 'pool_size','max_overflow' sent to create_engine(), using configuration SQLiteDialect_aiosqlite/NullPool/Engine.  Please check that the keyword arguments are appropriate for this combination of components.", "module": "database", "function": "init_database", "line": 196}
{"timestamp": "2025-07-16T15:32:05.676184", "level": "INFO", "logger": "src.utils.logger", "message": "Logging system initialized", "module": "logger", "function": "setup_logging", "line": 196}
{"timestamp": "2025-07-16T15:32:05.717034", "level": "INFO", "logger": "src.main", "message": "Starting QuantEdgeFlow application...", "module": "main", "function": "lifespan", "line": 35}
{"timestamp": "2025-07-16T15:32:05.730862", "level": "INFO", "logger": "src.config.database", "message": "Async database engine initialized", "module": "database", "function": "initialize_async_engine", "line": 116}
{"timestamp": "2025-07-16T15:32:05.731862", "level": "ERROR", "logger": "src.config.database", "message": "Database initialization failed: Invalid argument(s) 'pool_size','max_overflow' sent to create_engine(), using configuration SQLiteDialect_aiosqlite/NullPool/Engine.  Please check that the keyword arguments are appropriate for this combination of components.", "module": "database", "function": "init_database", "line": 212}
{"timestamp": "2025-07-16T15:32:45.267097", "level": "INFO", "logger": "src.utils.logger", "message": "Logging system initialized", "module": "logger", "function": "setup_logging", "line": 196}
{"timestamp": "2025-07-16T15:32:45.306993", "level": "INFO", "logger": "src.main", "message": "Starting QuantEdgeFlow application...", "module": "main", "function": "lifespan", "line": 35}
{"timestamp": "2025-07-16T15:32:45.317539", "level": "INFO", "logger": "src.config.database", "message": "Async database engine initialized", "module": "database", "function": "initialize_async_engine", "line": 116}
{"timestamp": "2025-07-16T15:32:45.317539", "level": "INFO", "logger": "src.config.database", "message": "Sync database engine initialized", "module": "database", "function": "initialize_sync_engine", "line": 165}
{"timestamp": "2025-07-16T15:32:45.325485", "level": "ERROR", "logger": "src.config.database", "message": "Database initialization failed: (sqlite3.OperationalError) near \"SET\": syntax error\n(Background on this error at: https://sqlalche.me/e/20/e3q8)", "module": "database", "function": "init_database", "line": 230}
{"timestamp": "2025-07-16T15:56:04.449494", "level": "INFO", "logger": "src.utils.logger", "message": "Logging system initialized", "module": "logger", "function": "setup_logging", "line": 196}
{"timestamp": "2025-07-16T15:56:04.494598", "level": "INFO", "logger": "src.main", "message": "Starting QuantEdgeFlow application...", "module": "main", "function": "lifespan", "line": 35}
{"timestamp": "2025-07-16T15:56:04.511356", "level": "INFO", "logger": "src.config.database", "message": "Async database engine initialized", "module": "database", "function": "initialize_async_engine", "line": 124}
{"timestamp": "2025-07-16T15:56:04.511356", "level": "INFO", "logger": "src.config.database", "message": "Sync database engine initialized", "module": "database", "function": "initialize_sync_engine", "line": 173}
{"timestamp": "2025-07-16T15:56:04.520909", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.521916", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"portfolios\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.521916", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.524014", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"portfolios\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.524284", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.525292", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"portfolio_positions\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.525802", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.525802", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"portfolio_positions\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.526811", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.526811", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"trades\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.527812", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.527812", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"trades\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.528810", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.528810", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"trade_legs\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.528810", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.529812", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"trade_legs\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.529812", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.530810", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"option_contracts\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.530810", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.531810", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"option_contracts\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.531810", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.532813", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"option_chains\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.532813", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.534590", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"option_chains\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.534590", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.535591", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"volatility_surfaces\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.535591", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.536590", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"volatility_surfaces\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.536590", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.537590", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"stock_quotes\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.537590", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.538590", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"stock_quotes\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.538590", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.539590", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"historical_prices\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.539590", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.540590", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"historical_prices\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.540590", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.541591", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"intraday_prices\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.541591", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.542591", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"intraday_prices\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.542591", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.543590", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"market_hours\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.543983", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.544457", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"market_hours\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.544457", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.545457", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"economic_indicators\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.545457", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.546459", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"economic_indicators\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.546459", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.548459", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:04.549458", "level": "ERROR", "logger": "src.config.database", "message": "Database initialization failed: (in table 'portfolios', column 'id'): Compiler <sqlalchemy.dialects.sqlite.base.SQLiteTypeCompiler object at 0x00000234B9234110> can't render element of type UUID", "module": "database", "function": "init_database", "line": 238}
{"timestamp": "2025-07-16T15:56:41.692955", "level": "INFO", "logger": "src.utils.logger", "message": "Logging system initialized", "module": "logger", "function": "setup_logging", "line": 196}
{"timestamp": "2025-07-16T15:56:41.728910", "level": "INFO", "logger": "src.main", "message": "Starting QuantEdgeFlow application...", "module": "main", "function": "lifespan", "line": 35}
{"timestamp": "2025-07-16T15:56:41.740430", "level": "INFO", "logger": "src.config.database", "message": "Async database engine initialized", "module": "database", "function": "initialize_async_engine", "line": 124}
{"timestamp": "2025-07-16T15:56:41.740430", "level": "INFO", "logger": "src.config.database", "message": "Sync database engine initialized", "module": "database", "function": "initialize_sync_engine", "line": 173}
{"timestamp": "2025-07-16T15:56:41.744430", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.744430", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"portfolios\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.744430", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.746046", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"portfolios\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.746046", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.747051", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"portfolio_positions\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.747051", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.748050", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"portfolio_positions\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.748050", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.749048", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"trades\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.749048", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.750050", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"trades\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.750050", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.751048", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"trade_legs\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.751048", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.752049", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"trade_legs\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.752049", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.753048", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"option_contracts\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.753048", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.754048", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"option_contracts\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.754048", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.755048", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"option_chains\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.755048", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.756048", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"option_chains\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.756048", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.756048", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"volatility_surfaces\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.757048", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.757048", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"volatility_surfaces\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.758138", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.758138", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"stock_quotes\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.759135", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.759135", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"stock_quotes\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.759135", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.760137", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"historical_prices\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.760137", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.761707", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"historical_prices\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.761707", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.762605", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"intraday_prices\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.762605", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.763603", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"intraday_prices\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.763603", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.764605", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"market_hours\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.764605", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.765603", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"market_hours\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.765603", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.766605", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"economic_indicators\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.766605", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.767603", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"economic_indicators\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.767603", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.768605", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:41.769606", "level": "ERROR", "logger": "src.config.database", "message": "Database initialization failed: (in table 'portfolios', column 'id'): Compiler <sqlalchemy.dialects.sqlite.base.SQLiteTypeCompiler object at 0x000002C1F7F846B0> can't render element of type UUID", "module": "database", "function": "init_database", "line": 238}
{"timestamp": "2025-07-16T15:56:49.927513", "level": "INFO", "logger": "src.utils.logger", "message": "Logging system initialized", "module": "logger", "function": "setup_logging", "line": 196}
{"timestamp": "2025-07-16T15:56:49.965157", "level": "INFO", "logger": "src.main", "message": "Starting QuantEdgeFlow application...", "module": "main", "function": "lifespan", "line": 35}
{"timestamp": "2025-07-16T15:56:49.976680", "level": "INFO", "logger": "src.config.database", "message": "Async database engine initialized", "module": "database", "function": "initialize_async_engine", "line": 124}
{"timestamp": "2025-07-16T15:56:49.976680", "level": "INFO", "logger": "src.config.database", "message": "Sync database engine initialized", "module": "database", "function": "initialize_sync_engine", "line": 173}
{"timestamp": "2025-07-16T15:56:49.982682", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:49.983682", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"portfolios\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:49.983682", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:49.984682", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"portfolios\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:49.984682", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:49.985683", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"portfolio_positions\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:49.985683", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:49.986682", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"portfolio_positions\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:49.986682", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:49.987682", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"trades\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:49.987682", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:49.988682", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"trades\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:49.988682", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:49.989683", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"trade_legs\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:49.989683", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:49.990682", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"trade_legs\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:49.990682", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:49.991682", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"option_contracts\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:49.991682", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:49.992645", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"option_contracts\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:49.993167", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:49.993167", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"option_chains\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:49.993167", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:49.994165", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"option_chains\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:49.994165", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:49.995163", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"volatility_surfaces\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:49.996164", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:49.996308", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"volatility_surfaces\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:49.996308", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:49.997309", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"stock_quotes\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:49.997309", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:49.998309", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"stock_quotes\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:49.998309", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:49.999309", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"historical_prices\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:49.999309", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:50.000311", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"historical_prices\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:50.000311", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:50.001311", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"intraday_prices\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:50.001311", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:50.002313", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"intraday_prices\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:50.002313", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:50.003312", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"market_hours\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:50.003312", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:50.004310", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"market_hours\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:50.004310", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:50.004310", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"economic_indicators\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:50.005311", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:50.006208", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"economic_indicators\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:50.006730", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:50.007237", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T15:56:50.008247", "level": "ERROR", "logger": "src.config.database", "message": "Database initialization failed: (in table 'portfolios', column 'id'): Compiler <sqlalchemy.dialects.sqlite.base.SQLiteTypeCompiler object at 0x000001FB17C60710> can't render element of type UUID", "module": "database", "function": "init_database", "line": 238}
{"timestamp": "2025-07-16T16:01:10.020101", "level": "INFO", "logger": "src.utils.logger", "message": "Logging system initialized", "module": "logger", "function": "setup_logging", "line": 196}
{"timestamp": "2025-07-16T16:01:10.061118", "level": "INFO", "logger": "src.main", "message": "Starting QuantEdgeFlow application...", "module": "main", "function": "lifespan", "line": 35}
{"timestamp": "2025-07-16T16:01:10.061630", "level": "INFO", "logger": "src.config.database", "message": "Async database engine initialized", "module": "database", "function": "initialize_async_engine", "line": 124}
{"timestamp": "2025-07-16T16:01:10.061630", "level": "INFO", "logger": "src.config.database", "message": "Sync database engine initialized", "module": "database", "function": "initialize_sync_engine", "line": 173}
{"timestamp": "2025-07-16T16:01:10.073553", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.076122", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"portfolios\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.076122", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.077124", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"portfolios\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.078121", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.079123", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"portfolio_positions\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.079123", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.080123", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"portfolio_positions\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.080123", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.081121", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"trades\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.081324", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.082042", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"trades\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.082042", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.083046", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"trade_legs\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.083046", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.084045", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"trade_legs\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.084045", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.085045", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"option_contracts\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.085045", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.086288", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"option_contracts\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.086288", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.087305", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"option_chains\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.087831", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.088591", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"option_chains\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.088591", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.089870", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"volatility_surfaces\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.090315", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.090315", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"volatility_surfaces\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.090315", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.092214", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"stock_quotes\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.092214", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.093218", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"stock_quotes\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.093218", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.094219", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"historical_prices\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.094219", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.095216", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"historical_prices\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.095216", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.096569", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"intraday_prices\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.096569", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.097600", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"intraday_prices\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.097600", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.098119", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"market_hours\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.098119", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.098119", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"market_hours\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.098119", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.098119", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"economic_indicators\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.098119", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.098119", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"economic_indicators\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.098119", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.107140", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "\nCREATE TABLE portfolios (\n\tid CHAR(36) NOT NULL, \n\tuser_id VARCHAR(255) NOT NULL, \n\tname VARCHAR(255) NOT NULL, \n\tdescription TEXT, \n\tnav VARCHAR(50) NOT NULL, \n\tcash_balance VARCHAR(50) NOT NULL, \n\tinitial_capital VARCHAR(50) NOT NULL, \n\ttotal_delta FLOAT, \n\ttotal_gamma FLOAT, \n\ttotal_theta FLOAT, \n\ttotal_vega FLOAT, \n\ttotal_rho FLOAT, \n\ttotal_return FLOAT, \n\ttotal_return_pct FLOAT, \n\tmax_drawdown FLOAT, \n\tsharpe_ratio FLOAT, \n\twin_rate FLOAT, \n\tprofit_factor FLOAT, \n\trisk_tolerance VARCHAR(50), \n\tmax_position_size FLOAT, \n\tmax_sector_exposure FLOAT, \n\tcreated_at DATETIME, \n\tupdated_at DATETIME, \n\tlast_rebalanced DATETIME, \n\tis_active BOOLEAN, \n\tis_paper_trading BOOLEAN, \n\tPRIMARY KEY (id)\n)\n\n", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.107140", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00294s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.107140", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX ix_portfolios_user_id ON portfolios (user_id)", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.107140", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00078s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.107140", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "\nCREATE TABLE option_contracts (\n\tid CHAR(36) NOT NULL, \n\tsymbol VARCHAR(20) NOT NULL, \n\toption_symbol VARCHAR(50) NOT NULL, \n\toption_type VARCHAR(10) NOT NULL, \n\tstrike_price VARCHAR(50) NOT NULL, \n\texpiration_date DATE NOT NULL, \n\tdays_to_expiration INTEGER, \n\tcontract_size INTEGER, \n\tstyle VARCHAR(20), \n\texchange VARCHAR(10), \n\tbid VARCHAR(50), \n\task VARCHAR(50), \n\tlast_price VARCHAR(50), \n\tmark_price VARCHAR(50), \n\tvolume INTEGER, \n\topen_interest INTEGER, \n\tdelta FLOAT, \n\tgamma FLOAT, \n\ttheta FLOAT, \n\tvega FLOAT, \n\trho FLOAT, \n\timplied_volatility FLOAT, \n\thistorical_volatility FLOAT, \n\tintrinsic_value VARCHAR(50), \n\ttime_value VARCHAR(50), \n\tmoneyness FLOAT, \n\tlast_trade_time DATETIME, \n\tquote_time DATETIME, \n\tupdated_at DATETIME, \n\tPRIMARY KEY (id)\n)\n\n", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.107140", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00315s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.107140", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX idx_option_symbol_expiration ON option_contracts (symbol, expiration_date)", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.123040", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00130s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.123559", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX idx_option_strike_type ON option_contracts (strike_price, option_type)", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.123559", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00085s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.133466", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX ix_option_contracts_symbol ON option_contracts (symbol)", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.133466", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00062s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.133466", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX idx_option_expiration_dte ON option_contracts (expiration_date, days_to_expiration)", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.133466", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00053s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.133466", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE UNIQUE INDEX ix_option_contracts_option_symbol ON option_contracts (option_symbol)", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.138089", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00055s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.138089", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX ix_option_contracts_expiration_date ON option_contracts (expiration_date)", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.138089", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00063s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.138089", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "\nCREATE TABLE option_chains (\n\tid CHAR(36) NOT NULL, \n\tsymbol VARCHAR(20) NOT NULL, \n\texpiration_date DATE NOT NULL, \n\tunderlying_price VARCHAR(50), \n\tunderlying_change VARCHAR(50), \n\tunderlying_change_percent FLOAT, \n\ttotal_call_volume INTEGER, \n\ttotal_put_volume INTEGER, \n\ttotal_call_open_interest INTEGER, \n\ttotal_put_open_interest INTEGER, \n\tput_call_ratio FLOAT, \n\timplied_volatility_rank FLOAT, \n\timplied_volatility_percentile FLOAT, \n\thistorical_volatility FLOAT, \n\tmax_pain VARCHAR(50), \n\tgamma_exposure FLOAT, \n\tcreated_at DATETIME, \n\tupdated_at DATETIME, \n\tPRIMARY KEY (id)\n)\n\n", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.138089", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00217s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.138089", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX ix_option_chains_expiration_date ON option_chains (expiration_date)", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.146126", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00049s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.146126", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX ix_option_chains_symbol ON option_chains (symbol)", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.146126", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00044s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.146126", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "\nCREATE TABLE volatility_surfaces (\n\tid CHAR(36) NOT NULL, \n\tsymbol VARCHAR(20) NOT NULL, \n\tcalculation_date DATE NOT NULL, \n\tsurface_data JSON, \n\tatm_volatility FLOAT, \n\tvolatility_skew FLOAT, \n\tterm_structure_slope FLOAT, \n\tvega_weighted_iv FLOAT, \n\tgamma_weighted_iv FLOAT, \n\tcreated_at DATETIME, \n\tupdated_at DATETIME, \n\tPRIMARY KEY (id)\n)\n\n", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.146126", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00126s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.152221", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX ix_volatility_surfaces_symbol ON volatility_surfaces (symbol)", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.152414", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00070s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.152944", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX ix_volatility_surfaces_calculation_date ON volatility_surfaces (calculation_date)", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.152944", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00046s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.158810", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "\nCREATE TABLE stock_quotes (\n\tid CHAR(36) NOT NULL, \n\tsymbol VARCHAR(20) NOT NULL, \n\tcompany_name VARCHAR(255), \n\texchange VARCHAR(10), \n\tprice VARCHAR(50) NOT NULL, \n\topen_price VARCHAR(50), \n\thigh_price VARCHAR(50), \n\tlow_price VARCHAR(50), \n\tprevious_close VARCHAR(50), \n\tchange VARCHAR(50), \n\tchange_percent FLOAT, \n\tvolume INTEGER, \n\taverage_volume INTEGER, \n\tvolume_ratio FLOAT, \n\tbid VARCHAR(50), \n\task VARCHAR(50), \n\tbid_size INTEGER, \n\task_size INTEGER, \n\tspread VARCHAR(50), \n\tspread_percent FLOAT, \n\tmarket_cap VARCHAR(50), \n\tshares_outstanding INTEGER, \n\tfloat_shares INTEGER, \n\tday_range_low VARCHAR(50), \n\tday_range_high VARCHAR(50), \n\tweek_52_low VARCHAR(50), \n\tweek_52_high VARCHAR(50), \n\thistorical_volatility FLOAT, \n\tbeta FLOAT, \n\tdata_source VARCHAR(20), \n\tquote_type VARCHAR(20), \n\tmarket_status VARCHAR(20), \n\tquote_time DATETIME NOT NULL, \n\tcreated_at DATETIME, \n\tupdated_at DATETIME, \n\tPRIMARY KEY (id)\n)\n\n", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.158810", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00553s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.158810", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX idx_stock_symbol_time ON stock_quotes (symbol, quote_time)", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.158810", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00121s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.170883", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX idx_stock_updated ON stock_quotes (updated_at)", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.171217", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00125s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.174657", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX ix_stock_quotes_symbol ON stock_quotes (symbol)", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.175014", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00063s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.176054", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "\nCREATE TABLE historical_prices (\n\tid CHAR(36) NOT NULL, \n\tsymbol VARCHAR(20) NOT NULL, \n\tdate DATE NOT NULL, \n\topen_price VARCHAR(50) NOT NULL, \n\thigh_price VARCHAR(50) NOT NULL, \n\tlow_price VARCHAR(50) NOT NULL, \n\tclose_price VARCHAR(50) NOT NULL, \n\tadjusted_close VARCHAR(50), \n\tvolume INTEGER, \n\tchange VARCHAR(50), \n\tchange_percent FLOAT, \n\tvwap VARCHAR(50), \n\tsma_20 VARCHAR(50), \n\tsma_50 VARCHAR(50), \n\tsma_200 VARCHAR(50), \n\tema_12 VARCHAR(50), \n\tema_26 VARCHAR(50), \n\tdaily_return FLOAT, \n\tvolatility_20d FLOAT, \n\tdata_source VARCHAR(20), \n\tcreated_at DATETIME, \n\tPRIMARY KEY (id)\n)\n\n", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.176054", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00162s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.176054", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX idx_historical_date ON historical_prices (date)", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.176054", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00052s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.180367", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX ix_historical_prices_date ON historical_prices (date)", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.181364", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00045s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.182364", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE UNIQUE INDEX idx_historical_symbol_date ON historical_prices (symbol, date)", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.182364", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00046s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.183368", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX ix_historical_prices_symbol ON historical_prices (symbol)", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.184362", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00034s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.185822", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "\nCREATE TABLE intraday_prices (\n\tid CHAR(36) NOT NULL, \n\tsymbol VARCHAR(20) NOT NULL, \n\ttimestamp DATETIME NOT NULL, \n\topen_price VARCHAR(50), \n\thigh_price VARCHAR(50), \n\tlow_price VARCHAR(50), \n\tclose_price VARCHAR(50) NOT NULL, \n\tvolume INTEGER, \n\tinterval VARCHAR(10), \n\tdata_source VARCHAR(20), \n\tcreated_at DATETIME, \n\tPRIMARY KEY (id)\n)\n\n", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.186804", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00124s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.187806", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX ix_intraday_prices_timestamp ON intraday_prices (timestamp)", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.187806", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00035s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.188805", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX idx_intraday_interval ON intraday_prices (interval)", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.188805", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00046s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.189805", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX ix_intraday_prices_symbol ON intraday_prices (symbol)", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.190917", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00082s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.191844", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX idx_intraday_symbol_timestamp ON intraday_prices (symbol, timestamp)", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.191844", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00034s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.191844", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "\nCREATE TABLE market_hours (\n\tid CHAR(36) NOT NULL, \n\tdate DATE NOT NULL, \n\texchange VARCHAR(10) NOT NULL, \n\tpre_market_open TIME, \n\tmarket_open TIME, \n\tmarket_close TIME, \n\tafter_hours_close TIME, \n\tis_trading_day BOOLEAN, \n\tis_holiday BOOLEAN, \n\tholiday_name VARCHAR(100), \n\tis_early_close BOOLEAN, \n\tearly_close_time TIME, \n\tcreated_at DATETIME, \n\tPRIMARY KEY (id)\n)\n\n", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.191844", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00104s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.195925", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE UNIQUE INDEX idx_market_hours_date_exchange ON market_hours (date, exchange)", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.195956", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00042s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.196930", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX ix_market_hours_date ON market_hours (date)", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.196930", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00043s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.199931", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "\nCREATE TABLE economic_indicators (\n\tid CHAR(36) NOT NULL, \n\tname VARCHAR(255) NOT NULL, \n\tcode VARCHAR(50) NOT NULL, \n\tcategory VARCHAR(100), \n\tcountry VARCHAR(10), \n\tdate DATE NOT NULL, \n\tvalue VARCHAR(50), \n\tprevious_value VARCHAR(50), \n\tforecast_value VARCHAR(50), \n\tchange VARCHAR(50), \n\tchange_percent FLOAT, \n\timportance VARCHAR(10), \n\tmarket_impact VARCHAR(10), \n\tunit VARCHAR(50), \n\tfrequency VARCHAR(20), \n\tdata_source VARCHAR(20), \n\trelease_time DATETIME, \n\tcreated_at DATETIME, \n\tPRIMARY KEY (id)\n)\n\n", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.200945", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00174s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.200945", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX idx_economic_code_date ON economic_indicators (code, date)", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.200945", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00052s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.200945", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX ix_economic_indicators_code ON economic_indicators (code)", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.200945", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00040s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.200945", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX idx_economic_category ON economic_indicators (category)", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.200945", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00038s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.207164", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX ix_economic_indicators_date ON economic_indicators (date)", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.207164", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00059s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.212301", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "\nCREATE TABLE portfolio_positions (\n\tid CHAR(36) NOT NULL, \n\tportfolio_id CHAR(36) NOT NULL, \n\tsymbol VARCHAR(20) NOT NULL, \n\tinstrument_type VARCHAR(20) NOT NULL, \n\tposition_type VARCHAR(10) NOT NULL, \n\tstatus VARCHAR(20), \n\tquantity INTEGER NOT NULL, \n\tavg_cost VARCHAR(50) NOT NULL, \n\tcurrent_price VARCHAR(50), \n\tmarket_value VARCHAR(50), \n\toption_type VARCHAR(10), \n\tstrike_price VARCHAR(50), \n\texpiration_date DATE, \n\tdelta FLOAT, \n\tgamma FLOAT, \n\ttheta FLOAT, \n\tvega FLOAT, \n\trho FLOAT, \n\timplied_volatility FLOAT, \n\tunrealized_pnl VARCHAR(50), \n\trealized_pnl VARCHAR(50), \n\ttotal_pnl VARCHAR(50), \n\tvar_1d FLOAT, \n\tvar_5d FLOAT, \n\tbeta FLOAT, \n\topened_at DATETIME, \n\tclosed_at DATETIME, \n\tlast_updated DATETIME, \n\tPRIMARY KEY (id), \n\tFOREIGN KEY(portfolio_id) REFERENCES portfolios (id)\n)\n\n", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.214547", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00377s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.217146", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX ix_portfolio_positions_symbol ON portfolio_positions (symbol)", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.217146", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00055s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.220970", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "\nCREATE TABLE trades (\n\tid CHAR(36) NOT NULL, \n\tportfolio_id CHAR(36) NOT NULL, \n\tuser_id VARCHAR(255) NOT NULL, \n\texternal_order_id VARCHAR(255), \n\tstrategy_name VARCHAR(100), \n\ttrade_strategy VARCHAR(50), \n\tsymbol VARCHAR(20) NOT NULL, \n\tinstrument_type VARCHAR(20) NOT NULL, \n\ttrade_type VARCHAR(20) NOT NULL, \n\torder_type VARCHAR(20) NOT NULL, \n\ttime_in_force VARCHAR(10), \n\tquantity INTEGER NOT NULL, \n\tfilled_quantity INTEGER, \n\tprice VARCHAR(50), \n\tfilled_price VARCHAR(50), \n\tstop_price VARCHAR(50), \n\toption_type VARCHAR(10), \n\tstrike_price VARCHAR(50), \n\texpiration_date DATE, \n\tcommission VARCHAR(50), \n\tfees VARCHAR(50), \n\ttotal_cost VARCHAR(50), \n\trealized_pnl VARCHAR(50), \n\tunrealized_pnl VARCHAR(50), \n\tdelta FLOAT, \n\tgamma FLOAT, \n\ttheta FLOAT, \n\tvega FLOAT, \n\trho FLOAT, \n\timplied_volatility FLOAT, \n\tstatus VARCHAR(20), \n\tnotes TEXT, \n\ttags JSON, \n\tcreated_at DATETIME, \n\tsubmitted_at DATETIME, \n\tfilled_at DATETIME, \n\tcancelled_at DATETIME, \n\tupdated_at DATETIME, \n\tmax_loss VARCHAR(50), \n\ttarget_profit VARCHAR(50), \n\tstop_loss_price VARCHAR(50), \n\ttake_profit_price VARCHAR(50), \n\tPRIMARY KEY (id), \n\tFOREIGN KEY(portfolio_id) REFERENCES portfolios (id)\n)\n\n", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.220970", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00351s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.220970", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX ix_trades_symbol ON trades (symbol)", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.220970", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00042s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.226670", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX ix_trades_user_id ON trades (user_id)", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.226670", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00037s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.229665", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "\nCREATE TABLE trade_legs (\n\tid CHAR(36) NOT NULL, \n\ttrade_id CHAR(36) NOT NULL, \n\tleg_number INTEGER NOT NULL, \n\tsymbol VARCHAR(20) NOT NULL, \n\tinstrument_type VARCHAR(20) NOT NULL, \n\ttrade_type VARCHAR(20) NOT NULL, \n\tquantity INTEGER NOT NULL, \n\tprice VARCHAR(50), \n\tfilled_price VARCHAR(50), \n\toption_type VARCHAR(10), \n\tstrike_price VARCHAR(50), \n\texpiration_date DATE, \n\tstatus VARCHAR(20), \n\tfilled_quantity INTEGER, \n\tdelta FLOAT, \n\tgamma FLOAT, \n\ttheta FLOAT, \n\tvega FLOAT, \n\trho FLOAT, \n\tcreated_at DATETIME, \n\tfilled_at DATETIME, \n\tPRIMARY KEY (id), \n\tFOREIGN KEY(trade_id) REFERENCES trades (id)\n)\n\n", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.230665", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00207s] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.231177", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:01:10.234334", "level": "INFO", "logger": "src.config.database", "message": "Database initialization completed successfully", "module": "database", "function": "init_database", "line": 235}
{"timestamp": "2025-07-16T16:02:08.561243", "level": "INFO", "logger": "src.utils.logger", "message": "Logging system initialized", "module": "logger", "function": "setup_logging", "line": 196}
{"timestamp": "2025-07-16T16:02:08.598032", "level": "INFO", "logger": "src.main", "message": "Starting QuantEdgeFlow application...", "module": "main", "function": "lifespan", "line": 35}
{"timestamp": "2025-07-16T16:02:08.607170", "level": "INFO", "logger": "src.config.database", "message": "Async database engine initialized", "module": "database", "function": "initialize_async_engine", "line": 124}
{"timestamp": "2025-07-16T16:02:08.607684", "level": "INFO", "logger": "src.config.database", "message": "Sync database engine initialized", "module": "database", "function": "initialize_sync_engine", "line": 173}
{"timestamp": "2025-07-16T16:02:08.611539", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:02:08.611539", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"portfolios\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:02:08.611539", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:02:08.612535", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"portfolio_positions\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:02:08.613574", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:02:08.613574", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"trades\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:02:08.613574", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:02:08.613574", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"trade_legs\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:02:08.613574", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:02:08.613574", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"option_contracts\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:02:08.613574", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:02:08.617207", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"option_chains\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:02:08.617207", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:02:08.618209", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"volatility_surfaces\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:02:08.618209", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:02:08.619209", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"stock_quotes\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:02:08.619209", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:02:08.620209", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"historical_prices\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:02:08.620209", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:02:08.621206", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"intraday_prices\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:02:08.621791", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:02:08.622310", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"market_hours\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:02:08.622310", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:02:08.622310", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"economic_indicators\")", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:02:08.622310", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:02:08.622310", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT", "module": "log", "function": "log", "line": 210}
{"timestamp": "2025-07-16T16:02:08.622310", "level": "INFO", "logger": "src.config.database", "message": "Database initialization completed successfully", "module": "database", "function": "init_database", "line": 235}
{"timestamp": "2025-07-16T16:02:08.622310", "level": "INFO", "logger": "src.main", "message": "QuantEdgeFlow application started successfully", "module": "main", "function": "lifespan", "line": 48}
{"timestamp": "2025-07-16T16:02:08.622310", "level": "INFO", "logger": "src.data.collectors.market_data", "message": "\ud83d\ude80 Starting real-time market data collection...", "module": "market_data", "function": "start_real_time_collection", "line": 487}
{"timestamp": "2025-07-16T16:02:08.622310", "level": "INFO", "logger": "src.data.collectors.market_data", "message": "\u2705 Real-time market data collection service is ready", "module": "market_data", "function": "start_real_time_collection", "line": 496}
