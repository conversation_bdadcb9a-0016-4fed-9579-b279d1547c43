#!/usr/bin/env python3
"""
Quick script to fix model imports for cross-database compatibility
"""

import os
import re
from pathlib import Path

def fix_model_file(file_path: Path):
    """Fix imports in a single model file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Replace PostgreSQL-specific imports with custom types
        replacements = [
            # UUID import
            (r'from sqlalchemy\.dialects\.postgresql import UUID', 
             'from src.core.database.types import UUID'),
            
            # DECIMAL import
            (r'from sqlalchemy\.dialects\.postgresql import.*DECIMAL', 
             'from src.core.database.types import DECIMAL'),
            
            # JSONB import
            (r'from sqlalchemy\.dialects\.postgresql import.*JSONB', 
             'from src.core.database.types import JSON'),
            
            # Replace JSONB with JSON
            (r'\bJSONB\b', 'JSON'),
            
            # Replace Numeric with DECIMAL
            (r'\bNumeric\(', 'DECIMAL('),
            
            # Remove Numeric from imports
            (r',\s*Numeric', ''),
            (r'Numeric,\s*', ''),
            (r'from sqlalchemy import.*Numeric.*\n', ''),
            
            # Add custom types import if not present
        ]
        
        for pattern, replacement in replacements:
            content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
        
        # Add custom types import if UUID, DECIMAL, or JSON are used but not imported
        if ('UUID(' in content or 'DECIMAL(' in content or 'JSON(' in content) and \
           'from src.core.database.types import' not in content:
            
            # Find the last sqlalchemy import
            import_match = re.search(r'(from sqlalchemy.*?\n)', content, re.MULTILINE | re.DOTALL)
            if import_match:
                import_end = import_match.end()
                
                # Determine what to import
                imports = []
                if 'UUID(' in content:
                    imports.append('UUID')
                if 'DECIMAL(' in content:
                    imports.append('DECIMAL')
                if 'JSON(' in content:
                    imports.append('JSON')
                
                if imports:
                    import_line = f"from src.core.database.types import {', '.join(imports)}\n"
                    content = content[:import_end] + import_line + content[import_end:]
        
        # Only write if content changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Fixed model imports in {file_path}")
            return True
        
        return False
        
    except Exception as e:
        print(f"❌ Error fixing {file_path}: {e}")
        return False

def main():
    """Main function to fix all model imports."""
    src_dir = Path("src")
    
    if not src_dir.exists():
        print("❌ src directory not found!")
        return
    
    print("🔧 Fixing model imports for cross-database compatibility...")
    
    fixed_count = 0
    total_count = 0
    
    # Find all Python files in models directories
    for py_file in src_dir.rglob("*.py"):
        if "models" in str(py_file) and py_file.name != "__init__.py":
            total_count += 1
            if fix_model_file(py_file):
                fixed_count += 1
    
    print(f"\n📊 Summary:")
    print(f"   Total model files processed: {total_count}")
    print(f"   Files with fixes: {fixed_count}")
    print(f"   Files unchanged: {total_count - fixed_count}")
    
    if fixed_count > 0:
        print("\n✅ Model import fixes completed!")
    else:
        print("\n✅ No model import fixes needed!")

if __name__ == "__main__":
    main()
