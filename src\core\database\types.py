"""
Custom database types for QuantEdgeFlow
Provides cross-database compatibility for custom types
"""

import uuid
from typing import Any, Optional

from sqlalchemy import TypeDecorator, String, Text
from sqlalchemy.dialects import postgresql, sqlite
from sqlalchemy.types import CHAR


class UUID(TypeDecorator):
    """
    Platform-independent UUID type.
    
    Uses PostgreSQL's UUID type when available,
    otherwise uses CHAR(36) for other databases.
    """
    
    impl = CHAR
    cache_ok = True
    
    def load_dialect_impl(self, dialect):
        """Load the appropriate implementation for the dialect."""
        if dialect.name == 'postgresql':
            return dialect.type_descriptor(postgresql.UUID())
        else:
            return dialect.type_descriptor(CHAR(36))
    
    def process_bind_param(self, value: Any, dialect) -> Optional[str]:
        """Process values being bound to the database."""
        if value is None:
            return value
        elif dialect.name == 'postgresql':
            return str(value)
        else:
            if not isinstance(value, uuid.UUID):
                return str(uuid.UUID(value))
            else:
                return str(value)
    
    def process_result_value(self, value: Any, dialect) -> Optional[uuid.UUID]:
        """Process values being returned from the database."""
        if value is None:
            return value
        else:
            if not isinstance(value, uuid.UUID):
                return uuid.UUID(value)
            return value


class JSON(TypeDecorator):
    """
    Platform-independent JSON type.
    
    Uses PostgreSQL's JSONB type when available,
    otherwise uses TEXT for other databases with JSON serialization.
    """
    
    impl = Text
    cache_ok = True
    
    def load_dialect_impl(self, dialect):
        """Load the appropriate implementation for the dialect."""
        if dialect.name == 'postgresql':
            return dialect.type_descriptor(postgresql.JSONB())
        else:
            return dialect.type_descriptor(Text())
    
    def process_bind_param(self, value: Any, dialect) -> Optional[str]:
        """Process values being bound to the database."""
        if value is None:
            return value
        
        if dialect.name == 'postgresql':
            return value
        else:
            import json
            return json.dumps(value)
    
    def process_result_value(self, value: Any, dialect) -> Any:
        """Process values being returned from the database."""
        if value is None:
            return value
        
        if dialect.name == 'postgresql':
            return value
        else:
            import json
            if isinstance(value, str):
                return json.loads(value)
            return value


class DECIMAL(TypeDecorator):
    """
    Platform-independent DECIMAL type with consistent precision.
    """
    
    impl = String
    cache_ok = True
    
    def __init__(self, precision=20, scale=8):
        """Initialize with precision and scale."""
        self.precision = precision
        self.scale = scale
        super().__init__()
    
    def load_dialect_impl(self, dialect):
        """Load the appropriate implementation for the dialect."""
        if dialect.name == 'postgresql':
            from sqlalchemy import DECIMAL as PG_DECIMAL
            return dialect.type_descriptor(PG_DECIMAL(self.precision, self.scale))
        else:
            # SQLite doesn't have native DECIMAL, use TEXT
            return dialect.type_descriptor(String(50))
    
    def process_bind_param(self, value: Any, dialect) -> Any:
        """Process values being bound to the database."""
        if value is None:
            return value
        
        if dialect.name == 'postgresql':
            return value
        else:
            # Convert to string for SQLite
            from decimal import Decimal
            if isinstance(value, Decimal):
                return str(value)
            return str(Decimal(str(value)))
    
    def process_result_value(self, value: Any, dialect) -> Any:
        """Process values being returned from the database."""
        if value is None:
            return value
        
        from decimal import Decimal
        if isinstance(value, str):
            return Decimal(value)
        return Decimal(str(value))


# Export commonly used types
__all__ = [
    'UUID',
    'JSON', 
    'DECIMAL'
]
