"""
Portfolio Data Models for QuantEdgeFlow
Comprehensive portfolio management with risk metrics and position tracking
"""

from datetime import datetime, date
from decimal import Decimal
from typing import List, Optional, Dict, Any
from enum import Enum
import uuid

from sqlalchemy import (
    Column, String, Float, Integer, DateTime, Boolean,
    Text, ForeignKey, Date
)
from sqlalchemy.orm import relationship, validates
from pydantic import BaseModel, Field, validator
from pydantic.types import condecimal

from src.config.database import Base
from src.core.database.types import UUID, DECIMAL, JSON


class PositionType(str, Enum):
    """Position type enumeration."""
    LONG = "long"
    SHORT = "short"


class PositionStatus(str, Enum):
    """Position status enumeration."""
    OPEN = "open"
    CLOSED = "closed"
    EXPIRED = "expired"


class Portfolio(Base):
    """Portfolio database model."""

    __tablename__ = "portfolios"

    id = Column(UUID(), primary_key=True, default=uuid.uuid4)
    user_id = Column(String(255), nullable=False, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text)

    # Financial metrics
    nav = Column(DECIMAL(15, 2), nullable=False, default=0)  # Net Asset Value
    cash_balance = Column(DECIMAL(15, 2), nullable=False, default=0)
    initial_capital = Column(DECIMAL(15, 2), nullable=False)

    # Risk metrics
    total_delta = Column(Float, default=0.0)
    total_gamma = Column(Float, default=0.0)
    total_theta = Column(Float, default=0.0)
    total_vega = Column(Float, default=0.0)
    total_rho = Column(Float, default=0.0)

    # Performance metrics
    total_return = Column(Float, default=0.0)
    total_return_pct = Column(Float, default=0.0)
    max_drawdown = Column(Float, default=0.0)
    sharpe_ratio = Column(Float, default=0.0)
    win_rate = Column(Float, default=0.0)
    profit_factor = Column(Float, default=0.0)

    # Portfolio settings
    risk_tolerance = Column(String(50), default="moderate")  # conservative, moderate, aggressive
    max_position_size = Column(Float, default=0.1)  # 10% max position size
    max_sector_exposure = Column(Float, default=0.3)  # 30% max sector exposure

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_rebalanced = Column(DateTime)

    # Status
    is_active = Column(Boolean, default=True)
    is_paper_trading = Column(Boolean, default=True)

    # Relationships
    positions = relationship("PortfolioPosition", back_populates="portfolio", cascade="all, delete-orphan")
    trades = relationship("Trade", back_populates="portfolio")

    @validates('risk_tolerance')
    def validate_risk_tolerance(self, key, value):
        """Validate risk tolerance values."""
        allowed_values = ['conservative', 'moderate', 'aggressive']
        if value not in allowed_values:
            raise ValueError(f"Risk tolerance must be one of: {allowed_values}")
        return value

    def calculate_nav(self) -> Decimal:
        """Calculate current Net Asset Value."""
        position_value = sum(pos.market_value for pos in self.positions if pos.status == PositionStatus.OPEN)
        return self.cash_balance + position_value

    def calculate_total_greeks(self) -> Dict[str, float]:
        """Calculate total portfolio Greeks."""
        total_delta = sum(pos.delta * pos.quantity for pos in self.positions if pos.status == PositionStatus.OPEN)
        total_gamma = sum(pos.gamma * pos.quantity for pos in self.positions if pos.status == PositionStatus.OPEN)
        total_theta = sum(pos.theta * pos.quantity for pos in self.positions if pos.status == PositionStatus.OPEN)
        total_vega = sum(pos.vega * pos.quantity for pos in self.positions if pos.status == PositionStatus.OPEN)
        total_rho = sum(pos.rho * pos.quantity for pos in self.positions if pos.status == PositionStatus.OPEN)

        return {
            "delta": total_delta,
            "gamma": total_gamma,
            "theta": total_theta,
            "vega": total_vega,
            "rho": total_rho
        }


class PortfolioPosition(Base):
    """Portfolio position database model."""

    __tablename__ = "portfolio_positions"

    id = Column(UUID(), primary_key=True, default=uuid.uuid4)
    portfolio_id = Column(UUID(), ForeignKey("portfolios.id"), nullable=False)

    # Position details
    symbol = Column(String(20), nullable=False, index=True)
    instrument_type = Column(String(20), nullable=False)  # stock, option, future
    position_type = Column(String(10), nullable=False)  # long, short
    status = Column(String(20), default=PositionStatus.OPEN)

    # Quantity and pricing
    quantity = Column(Integer, nullable=False)
    avg_cost = Column(DECIMAL(10, 4), nullable=False)
    current_price = Column(DECIMAL(10, 4))
    market_value = Column(DECIMAL(15, 2))

    # Options-specific fields
    option_type = Column(String(10))  # call, put
    strike_price = Column(DECIMAL(10, 4))
    expiration_date = Column(Date)

    # Greeks (for options)
    delta = Column(Float, default=0.0)
    gamma = Column(Float, default=0.0)
    theta = Column(Float, default=0.0)
    vega = Column(Float, default=0.0)
    rho = Column(Float, default=0.0)
    implied_volatility = Column(Float)

    # P&L tracking
    unrealized_pnl = Column(DECIMAL(15, 2), default=0)
    realized_pnl = Column(DECIMAL(15, 2), default=0)
    total_pnl = Column(DECIMAL(15, 2), default=0)

    # Risk metrics
    var_1d = Column(Float)  # 1-day Value at Risk
    var_5d = Column(Float)  # 5-day Value at Risk
    beta = Column(Float)

    # Timestamps
    opened_at = Column(DateTime, default=datetime.utcnow)
    closed_at = Column(DateTime)
    last_updated = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    portfolio = relationship("Portfolio", back_populates="positions")

    @validates('position_type')
    def validate_position_type(self, key, value):
        """Validate position type."""
        if value not in [PositionType.LONG, PositionType.SHORT]:
            raise ValueError(f"Position type must be {PositionType.LONG} or {PositionType.SHORT}")
        return value

    @validates('instrument_type')
    def validate_instrument_type(self, key, value):
        """Validate instrument type."""
        allowed_types = ['stock', 'option', 'future', 'etf']
        if value not in allowed_types:
            raise ValueError(f"Instrument type must be one of: {allowed_types}")
        return value

    def calculate_pnl(self) -> Dict[str, Decimal]:
        """Calculate position P&L."""
        if not self.current_price:
            return {"unrealized": Decimal(0), "total": self.realized_pnl}

        if self.position_type == PositionType.LONG:
            unrealized = (self.current_price - self.avg_cost) * self.quantity
        else:  # SHORT
            unrealized = (self.avg_cost - self.current_price) * self.quantity

        total = unrealized + self.realized_pnl

        return {
            "unrealized": unrealized,
            "realized": self.realized_pnl,
            "total": total
        }


# Pydantic models for API
class PortfolioBase(BaseModel):
    """Base portfolio model for API."""
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    initial_capital: condecimal(gt=0, decimal_places=2)
    risk_tolerance: str = Field(default="moderate", pattern="^(conservative|moderate|aggressive)$")
    max_position_size: float = Field(default=0.1, ge=0.01, le=1.0)
    max_sector_exposure: float = Field(default=0.3, ge=0.1, le=1.0)
    is_paper_trading: bool = True


class PortfolioCreate(PortfolioBase):
    """Portfolio creation model."""
    pass


class PortfolioUpdate(BaseModel):
    """Portfolio update model."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    risk_tolerance: Optional[str] = Field(None, pattern="^(conservative|moderate|aggressive)$")
    max_position_size: Optional[float] = Field(None, ge=0.01, le=1.0)
    max_sector_exposure: Optional[float] = Field(None, ge=0.1, le=1.0)
    is_active: Optional[bool] = None


class PortfolioMetrics(BaseModel):
    """Portfolio metrics model."""
    nav: Decimal
    cash_balance: Decimal
    total_return: float
    total_return_pct: float
    max_drawdown: float
    sharpe_ratio: float
    win_rate: float
    profit_factor: float
    total_delta: float
    total_gamma: float
    total_theta: float
    total_vega: float
    total_rho: float
    portfolio_beta: float
    var_1d: Optional[float] = None
    var_5d: Optional[float] = None


class PortfolioPositionBase(BaseModel):
    """Base position model for API."""
    symbol: str = Field(..., min_length=1, max_length=20)
    instrument_type: str = Field(..., pattern="^(stock|option|future|etf)$")
    position_type: str = Field(..., pattern="^(long|short)$")
    quantity: int = Field(..., ne=0)
    avg_cost: condecimal(gt=0, decimal_places=4)

    # Options-specific fields
    option_type: Optional[str] = Field(None, pattern="^(call|put)$")
    strike_price: Optional[condecimal(gt=0, decimal_places=4)] = None
    expiration_date: Optional[date] = None

    @validator('expiration_date')
    def validate_expiration_date(cls, v, values):
        """Validate expiration date for options."""
        if values.get('instrument_type') == 'option' and v is None:
            raise ValueError('Expiration date is required for options')
        if v and v <= date.today():
            raise ValueError('Expiration date must be in the future')
        return v

    @validator('option_type')
    def validate_option_type(cls, v, values):
        """Validate option type for options."""
        if values.get('instrument_type') == 'option' and v is None:
            raise ValueError('Option type is required for options')
        return v

    @validator('strike_price')
    def validate_strike_price(cls, v, values):
        """Validate strike price for options."""
        if values.get('instrument_type') == 'option' and v is None:
            raise ValueError('Strike price is required for options')
        return v


class PortfolioPositionCreate(PortfolioPositionBase):
    """Position creation model."""
    pass


class PortfolioPositionResponse(PortfolioPositionBase):
    """Position response model."""
    id: uuid.UUID
    portfolio_id: uuid.UUID
    status: str
    current_price: Optional[Decimal] = None
    market_value: Optional[Decimal] = None
    delta: float = 0.0
    gamma: float = 0.0
    theta: float = 0.0
    vega: float = 0.0
    rho: float = 0.0
    implied_volatility: Optional[float] = None
    unrealized_pnl: Decimal = Decimal(0)
    realized_pnl: Decimal = Decimal(0)
    total_pnl: Decimal = Decimal(0)
    opened_at: datetime
    closed_at: Optional[datetime] = None
    last_updated: datetime

    class Config:
        from_attributes = True


class PortfolioResponse(PortfolioBase):
    """Portfolio response model."""
    id: uuid.UUID
    user_id: str
    nav: Decimal
    cash_balance: Decimal
    total_return: float
    total_return_pct: float
    max_drawdown: float
    sharpe_ratio: float
    win_rate: float
    profit_factor: float
    total_delta: float
    total_gamma: float
    total_theta: float
    total_vega: float
    total_rho: float
    created_at: datetime
    updated_at: datetime
    last_rebalanced: Optional[datetime] = None
    is_active: bool
    positions: List[PortfolioPositionResponse] = []

    class Config:
        from_attributes = True


class PortfolioMetricsResponse(BaseModel):
    """Portfolio metrics response model."""
    portfolio_id: uuid.UUID
    total_value: Decimal
    total_return: Decimal
    total_return_percentage: Decimal
    day_return: Decimal
    day_return_percentage: Decimal
    sharpe_ratio: Optional[Decimal] = None
    max_drawdown: Optional[Decimal] = None
    volatility: Optional[Decimal] = None
    beta: Optional[Decimal] = None
    alpha: Optional[Decimal] = None
    calculated_at: datetime

    class Config:
        from_attributes = True


# Response models for API
PositionCreate = PortfolioPositionCreate
PositionUpdate = PortfolioPositionCreate  # Use create model for updates
PositionResponse = PortfolioPositionResponse
