"""
Market Data Collector for QuantEdgeFlow
Real-time and historical market data collection from multiple sources
"""

import asyncio
import logging
from typing import List, Optional, Dict, Any, Union
from datetime import datetime, date, timedelta
from decimal import Decimal
import aiohttp
import yfinance as yf
from alpha_vantage.timeseries import TimeSeries
from alpha_vantage.fundamentaldata import FundamentalData

from src.config.settings import get_settings
from src.config.database import get_async_session
from src.core.models.market_data import (
    StockQuote, HistoricalPrice, IntradayPrice, MarketHours,
    DataSource, QuoteType, MarketStatus
)
from src.utils.logger import get_performance_logger


settings = get_settings()
logger = logging.getLogger(__name__)
performance_logger = get_performance_logger()

class MarketDataCollector:
    """Multi-source market data collector."""

    def __init__(self):
        self.logger = logger
        self.alpha_vantage_api_key = settings.ALPHA_VANTAGE_API_KEY
        self.polygon_api_key = settings.POLYGON_API_KEY
        self.session: Optional[aiohttp.ClientSession] = None

        # Rate limiting
        self.rate_limits = {
            DataSource.ALPHA_VANTAGE: {"calls_per_minute": 5, "last_call": None},
            DataSource.POLYGON: {"calls_per_minute": 100, "last_call": None},
            DataSource.YAHOO_FINANCE: {"calls_per_minute": 2000, "last_call": None}
        }

    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()

    async def get_real_time_quote(self, symbol: str) -> Optional[StockQuote]:
        """Get real-time stock quote."""
        try:
            start_time = datetime.utcnow()

            # Try Yahoo Finance first (free and reliable)
            quote = await self._get_yahoo_quote(symbol)

            if not quote and self.alpha_vantage_api_key:
                # Fallback to Alpha Vantage
                quote = await self._get_alpha_vantage_quote(symbol)

            if not quote and self.polygon_api_key:
                # Fallback to Polygon
                quote = await self._get_polygon_quote(symbol)

            if quote:
                # Store in database
                await self._store_quote(quote)

                # Log performance
                duration = (datetime.utcnow() - start_time).total_seconds()
                performance_logger.info(f"Real-time quote retrieved", extra={
                    "symbol": symbol,
                    "source": quote.data_source,
                    "duration_seconds": duration
                })

            return quote

        except Exception as e:
            self.logger.error(f"Failed to get real-time quote for {symbol}: {e}")
            return None

    async def get_historical_data(
        self,
        symbol: str,
        start_date: date,
        end_date: date,
        interval: str = "1d"
    ) -> List[HistoricalPrice]:
        """Get historical price data."""
        try:
            start_time = datetime.utcnow()

            # Use Yahoo Finance for historical data
            data = await self._get_yahoo_historical(symbol, start_date, end_date, interval)

            if not data and self.alpha_vantage_api_key:
                # Fallback to Alpha Vantage
                data = await self._get_alpha_vantage_historical(symbol, start_date, end_date)

            if data:
                # Store in database
                await self._store_historical_data(data)

                # Log performance
                duration = (datetime.utcnow() - start_time).total_seconds()
                performance_logger.info(f"Historical data retrieved", extra={
                    "symbol": symbol,
                    "records_count": len(data),
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "duration_seconds": duration
                })

            return data or []

        except Exception as e:
            self.logger.error(f"Failed to get historical data for {symbol}: {e}")
            return []

    async def get_intraday_data(
        self,
        symbol: str,
        interval: str = "5m",
        days: int = 1
    ) -> List[IntradayPrice]:
        """Get intraday price data."""
        try:
            start_time = datetime.utcnow()

            # Use Yahoo Finance for intraday data
            data = await self._get_yahoo_intraday(symbol, interval, days)

            if data:
                # Store in database
                await self._store_intraday_data(data)

                # Log performance
                duration = (datetime.utcnow() - start_time).total_seconds()
                performance_logger.info(f"Intraday data retrieved", extra={
                    "symbol": symbol,
                    "records_count": len(data),
                    "interval": interval,
                    "days": days,
                    "duration_seconds": duration
                })

            return data or []

        except Exception as e:
            self.logger.error(f"Failed to get intraday data for {symbol}: {e}")
            return []

    async def get_multiple_quotes(self, symbols: List[str]) -> Dict[str, StockQuote]:
        """Get quotes for multiple symbols efficiently."""
        try:
            start_time = datetime.utcnow()

            # Batch request using Yahoo Finance
            quotes = await self._get_yahoo_batch_quotes(symbols)

            # Store all quotes
            if quotes:
                await self._store_multiple_quotes(list(quotes.values()))

            # Log performance
            duration = (datetime.utcnow() - start_time).total_seconds()
            performance_logger.info(f"Batch quotes retrieved", extra={
                "symbols_count": len(symbols),
                "quotes_retrieved": len(quotes),
                "duration_seconds": duration
            })

            return quotes

        except Exception as e:
            self.logger.error(f"Failed to get multiple quotes: {e}")
            return {}

    async def _get_yahoo_quote(self, symbol: str) -> Optional[StockQuote]:
        """Get quote from Yahoo Finance."""
        try:
            await self._check_rate_limit(DataSource.YAHOO_FINANCE)

            ticker = yf.Ticker(symbol)
            info = ticker.info

            if not info or 'regularMarketPrice' not in info:
                return None

            quote = StockQuote(
                symbol=symbol.upper(),
                company_name=info.get('longName'),
                exchange=info.get('exchange'),
                price=Decimal(str(info['regularMarketPrice'])),
                open_price=Decimal(str(info.get('regularMarketOpen', 0))),
                high_price=Decimal(str(info.get('regularMarketDayHigh', 0))),
                low_price=Decimal(str(info.get('regularMarketDayLow', 0))),
                previous_close=Decimal(str(info.get('regularMarketPreviousClose', 0))),
                change=Decimal(str(info.get('regularMarketChange', 0))),
                change_percent=info.get('regularMarketChangePercent', 0),
                volume=info.get('regularMarketVolume', 0),
                average_volume=info.get('averageVolume', 0),
                bid=Decimal(str(info.get('bid', 0))) if info.get('bid') else None,
                ask=Decimal(str(info.get('ask', 0))) if info.get('ask') else None,
                bid_size=info.get('bidSize'),
                ask_size=info.get('askSize'),
                market_cap=Decimal(str(info.get('marketCap', 0))) if info.get('marketCap') else None,
                shares_outstanding=info.get('sharesOutstanding'),
                week_52_low=Decimal(str(info.get('fiftyTwoWeekLow', 0))) if info.get('fiftyTwoWeekLow') else None,
                week_52_high=Decimal(str(info.get('fiftyTwoWeekHigh', 0))) if info.get('fiftyTwoWeekHigh') else None,
                beta=info.get('beta'),
                data_source=DataSource.YAHOO_FINANCE,
                quote_type=QuoteType.REAL_TIME,
                quote_time=datetime.utcnow()
            )

            # Calculate derived fields
            if quote.bid and quote.ask:
                quote.spread = quote.ask - quote.bid
                quote.spread_percent = float(quote.spread / quote.price * 100) if quote.price > 0 else 0

            if quote.volume and quote.average_volume:
                quote.volume_ratio = quote.volume / quote.average_volume

            return quote

        except Exception as e:
            self.logger.warning(f"Failed to get Yahoo Finance quote for {symbol}: {e}")
            return None

    async def _get_yahoo_historical(
        self,
        symbol: str,
        start_date: date,
        end_date: date,
        interval: str = "1d"
    ) -> List[HistoricalPrice]:
        """Get historical data from Yahoo Finance."""
        try:
            await self._check_rate_limit(DataSource.YAHOO_FINANCE)

            ticker = yf.Ticker(symbol)
            hist = ticker.history(start=start_date, end=end_date, interval=interval)

            if hist.empty:
                return []

            historical_data = []
            for date_idx, row in hist.iterrows():
                price_data = HistoricalPrice(
                    symbol=symbol.upper(),
                    date=date_idx.date(),
                    open_price=Decimal(str(row['Open'])),
                    high_price=Decimal(str(row['High'])),
                    low_price=Decimal(str(row['Low'])),
                    close_price=Decimal(str(row['Close'])),
                    volume=int(row['Volume']),
                    data_source=DataSource.YAHOO_FINANCE
                )

                # Calculate change
                if len(historical_data) > 0:
                    prev_close = historical_data[-1].close_price
                    price_data.change = price_data.close_price - prev_close
                    price_data.change_percent = float(price_data.change / prev_close * 100) if prev_close > 0 else 0

                historical_data.append(price_data)

            return historical_data

        except Exception as e:
            self.logger.warning(f"Failed to get Yahoo Finance historical data for {symbol}: {e}")
            return []

    async def _get_yahoo_intraday(
        self,
        symbol: str,
        interval: str = "5m",
        days: int = 1
    ) -> List[IntradayPrice]:
        """Get intraday data from Yahoo Finance."""
        try:
            await self._check_rate_limit(DataSource.YAHOO_FINANCE)

            ticker = yf.Ticker(symbol)
            hist = ticker.history(period=f"{days}d", interval=interval)

            if hist.empty:
                return []

            intraday_data = []
            for timestamp, row in hist.iterrows():
                price_data = IntradayPrice(
                    symbol=symbol.upper(),
                    timestamp=timestamp.to_pydatetime(),
                    open_price=Decimal(str(row['Open'])),
                    high_price=Decimal(str(row['High'])),
                    low_price=Decimal(str(row['Low'])),
                    close_price=Decimal(str(row['Close'])),
                    volume=int(row['Volume']),
                    interval=interval,
                    data_source=DataSource.YAHOO_FINANCE
                )
                intraday_data.append(price_data)

            return intraday_data

        except Exception as e:
            self.logger.warning(f"Failed to get Yahoo Finance intraday data for {symbol}: {e}")
            return []

    async def _get_yahoo_batch_quotes(self, symbols: List[str]) -> Dict[str, StockQuote]:
        """Get batch quotes from Yahoo Finance."""
        try:
            await self._check_rate_limit(DataSource.YAHOO_FINANCE)

            # Yahoo Finance allows batch requests
            tickers = yf.Tickers(' '.join(symbols))
            quotes = {}

            for symbol in symbols:
                try:
                    ticker = tickers.tickers[symbol]
                    info = ticker.info

                    if info and 'regularMarketPrice' in info:
                        quote = await self._get_yahoo_quote(symbol)
                        if quote:
                            quotes[symbol] = quote

                except Exception as e:
                    self.logger.warning(f"Failed to get quote for {symbol} in batch: {e}")
                    continue

            return quotes

        except Exception as e:
            self.logger.warning(f"Failed to get batch quotes: {e}")
            return {}

    async def _get_alpha_vantage_quote(self, symbol: str) -> Optional[StockQuote]:
        """Get quote from Alpha Vantage."""
        if not self.alpha_vantage_api_key:
            return None

        try:
            await self._check_rate_limit(DataSource.ALPHA_VANTAGE)

            ts = TimeSeries(key=self.alpha_vantage_api_key, output_format='pandas')
            data, meta_data = ts.get_quote_endpoint(symbol)

            if data.empty:
                return None

            quote_data = data.iloc[0]

            quote = StockQuote(
                symbol=symbol.upper(),
                price=Decimal(str(quote_data['05. price'])),
                open_price=Decimal(str(quote_data['02. open'])),
                high_price=Decimal(str(quote_data['03. high'])),
                low_price=Decimal(str(quote_data['04. low'])),
                previous_close=Decimal(str(quote_data['08. previous close'])),
                change=Decimal(str(quote_data['09. change'])),
                change_percent=float(quote_data['10. change percent'].rstrip('%')),
                volume=int(quote_data['06. volume']),
                data_source=DataSource.ALPHA_VANTAGE,
                quote_type=QuoteType.REAL_TIME,
                quote_time=datetime.utcnow()
            )

            return quote

        except Exception as e:
            self.logger.warning(f"Failed to get Alpha Vantage quote for {symbol}: {e}")
            return None

    async def _get_polygon_quote(self, symbol: str) -> Optional[StockQuote]:
        """Get quote from Polygon.io."""
        if not self.polygon_api_key or not self.session:
            return None

        try:
            await self._check_rate_limit(DataSource.POLYGON)

            url = f"https://api.polygon.io/v2/snapshot/locale/us/markets/stocks/tickers/{symbol}"
            params = {"apikey": self.polygon_api_key}

            async with self.session.get(url, params=params) as response:
                if response.status != 200:
                    return None

                data = await response.json()

                if not data.get('results') or not data['results'].get('value'):
                    return None

                ticker_data = data['results']['value']

                quote = StockQuote(
                    symbol=symbol.upper(),
                    price=Decimal(str(ticker_data.get('c', 0))),  # Close price
                    open_price=Decimal(str(ticker_data.get('o', 0))),
                    high_price=Decimal(str(ticker_data.get('h', 0))),
                    low_price=Decimal(str(ticker_data.get('l', 0))),
                    previous_close=Decimal(str(ticker_data.get('pc', 0))),
                    volume=ticker_data.get('v', 0),
                    data_source=DataSource.POLYGON,
                    quote_type=QuoteType.REAL_TIME,
                    quote_time=datetime.utcnow()
                )

                return quote

        except Exception as e:
            self.logger.warning(f"Failed to get Polygon quote for {symbol}: {e}")
            return None

    async def _check_rate_limit(self, source: DataSource) -> None:
        """Check and enforce rate limits."""
        if source not in self.rate_limits:
            return

        limit_info = self.rate_limits[source]
        now = datetime.utcnow()

        if limit_info["last_call"]:
            time_since_last = (now - limit_info["last_call"]).total_seconds()
            min_interval = 60 / limit_info["calls_per_minute"]

            if time_since_last < min_interval:
                sleep_time = min_interval - time_since_last
                await asyncio.sleep(sleep_time)

        limit_info["last_call"] = now

    async def _store_quote(self, quote: StockQuote) -> None:
        """Store quote in database."""
        async with get_async_session() as session:
            try:
                session.add(quote)
                await session.commit()
            except Exception as e:
                await session.rollback()
                self.logger.error(f"Failed to store quote: {e}")

    async def _store_historical_data(self, data: List[HistoricalPrice]) -> None:
        """Store historical data in database."""
        async with get_async_session() as session:
            try:
                session.add_all(data)
                await session.commit()
            except Exception as e:
                await session.rollback()
                self.logger.error(f"Failed to store historical data: {e}")

    async def _store_intraday_data(self, data: List[IntradayPrice]) -> None:
        """Store intraday data in database."""
        async with get_async_session() as session:
            try:
                session.add_all(data)
                await session.commit()
            except Exception as e:
                await session.rollback()
                self.logger.error(f"Failed to store intraday data: {e}")

    async def _store_multiple_quotes(self, quotes: List[StockQuote]) -> None:
        """Store multiple quotes in database."""
        async with get_async_session() as session:
            try:
                session.add_all(quotes)
                await session.commit()
            except Exception as e:
                await session.rollback()
                self.logger.error(f"Failed to store multiple quotes: {e}")

    async def start_real_time_collection(self):
        """Start real-time market data collection."""
        try:
            self.logger.info("🚀 Starting real-time market data collection...")

            # This is a placeholder for real-time data collection
            # In a production environment, you would:
            # 1. Connect to real-time data feeds (WebSocket, etc.)
            # 2. Subscribe to market data streams
            # 3. Process incoming data in real-time

            # For now, we'll just log that the service is ready
            self.logger.info("✅ Real-time market data collection service is ready")

        except Exception as e:
            self.logger.error(f"❌ Failed to start real-time collection: {e}")
            raise

    async def stop_real_time_collection(self):
        """Stop real-time market data collection."""
        try:
            self.logger.info("🛑 Stopping real-time market data collection...")

            # This is a placeholder for stopping real-time data collection
            # In a production environment, you would:
            # 1. Disconnect from real-time data feeds
            # 2. Unsubscribe from market data streams
            # 3. Clean up resources

            self.logger.info("✅ Real-time market data collection stopped")

        except Exception as e:
            self.logger.error(f"❌ Failed to stop real-time collection: {e}")


# Global market data collector instance
market_data_collector = MarketDataCollector()
